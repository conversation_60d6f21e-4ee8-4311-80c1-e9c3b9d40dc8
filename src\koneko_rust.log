[1750997654.633172] [WARN]   Logger initialized successfully
[1750997654.636903] [INFO]   === Koneko Rust Implementation Starting ===
[1750997654.637404] [INFO]   !!! Sandbox checks disabled via command-line flag !!!
[1750997654.637563] [INFO]   !!! This is for testing purposes only !!!
[1750997654.637689] [INFO]   Initializing global variables
[1750997654.637861] [INFO]   Collecting call r12 gadgets
[1750997654.674784] [INFO]   Skipping sandbox/VM check (disabled via command-line flag)
[1750997654.675159] [INFO]   Starting main functionality
[1750997654.675541] [INFO]   Entering run_me() function
[1750997654.675737] [INFO]   Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)
[1750997654.675899] [INFO]   Skipping VDLL / Defender emulator check (disabled via command-line flag)
[1750997654.676008] [INFO]   Skipping debugger detection (disabled via command-line flag)
[1750997654.676291] [INFO]   Starting shellcode deobfuscation and preparation
[1750997654.676600] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750997654.676922] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750997654.677217] [INFO]   Allocating memory for shellcode
[1750997654.752234] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750997654.753002] [INFO]   NtAllocateVirtualMemory succeeded
[1750997654.753258] [ERROR]  NtAllocateVirtualMemory returned NULL base address
[1750997654.753570] [ERROR]  This suggests the syscall parameters are not being passed correctly
